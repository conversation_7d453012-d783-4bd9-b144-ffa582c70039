package com.wonderslate_v2

import usermanagement.User
import grails.gorm.transactions.Transactional

class AuthenticationController {

    def springSecurityService

    def index() {
        redirect(action: 'login')
    }

    def login() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

    @Transactional
    def authenticate() {
        String username = params.username
        String password = params.password

        if (!username || !password) {
            flash.error = "Please enter both username and password"
            redirect(action: 'login')
            return
        }

        User user = User.findByUsername(username)
        
        if (!user) {
            flash.error = "Invalid username or password"
            redirect(action: 'login')
            return
        }

        if (!user.enabled) {
            flash.error = "Account is disabled"
            redirect(action: 'login')
            return
        }

        // Check password using Spring Security's password encoder
        if (springSecurityService.passwordEncoder.matches(password, user.password)) {
            // Successful authentication
            session.user = user
            flash.success = "Welcome, ${user.name ?: user.username}!"
            redirect(controller: 'home', action: 'index')
        } else {
            flash.error = "Invalid username or password"
            redirect(action: 'login')
        }
    }

    def logout() {
        session.invalidate()
        flash.success = "You have been logged out successfully"
        redirect(action: 'login')
    }

    def register() {
        [:]
    }

    @Transactional
    def saveUser() {
        String username = params.username
        String password = params.password
        String confirmPassword = params.confirmPassword
        String email = params.email
        String name = params.name

        if (!username || !password || !email || !name) {
            flash.error = "Please fill in all required fields"
            redirect(action: 'register')
            return
        }

        if (password != confirmPassword) {
            flash.error = "Passwords do not match"
            redirect(action: 'register')
            return
        }

        if (User.findByUsername(username)) {
            flash.error = "Username already exists"
            redirect(action: 'register')
            return
        }

        if (User.findByEmail(email)) {
            flash.error = "Email already exists"
            redirect(action: 'register')
            return
        }

        try {
            User user = new User(
                username: username,
                password: password,
                email: email,
                name: name,
                enabled: true,
                accountExpired: false,
                accountLocked: false,
                passwordExpired: false,
                dateCreated: new Date()
            )

            if (user.save(flush: true)) {
                flash.success = "Registration successful! Please login."
                redirect(action: 'login')
            } else {
                flash.error = "Registration failed. Please try again."
                redirect(action: 'register')
            }
        } catch (Exception e) {
            log.error("Error during user registration: ${e.message}", e)
            flash.error = "Registration failed. Please try again."
            redirect(action: 'register')
        }
    }
}
