package com.wonderslate_v2

import usermanagement.User
import grails.gorm.transactions.Transactional

@Transactional('wsuser')
class UserService {

    def springSecurityService

    User findByUsername(String username) {
        return User.wsuser.findByUsername(username)
    }

    User findByEmail(String email) {
        return User.wsuser.findByEmail(email)
    }

    User createUser(String username, String password, String email, String name) {
        User user = new User(
            username: username,
            password: password,
            email: email,
            name: name,
            enabled: true,
            accountExpired: false,
            accountLocked: false,
            passwordExpired: false,
            dateCreated: new Date()
        )
        
        return user.save(flush: true, failOnError: true)
    }

    boolean validatePassword(String plainPassword, String encodedPassword) {
        println("Validating password: $plainPassword, $encodedPassword")
        return springSecurityService?.passwordEncoder?.matches(plainPassword, encodedPassword) ?: false
    }
}
